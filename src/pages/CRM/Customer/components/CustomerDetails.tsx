import ChangeLog from '@/components/ChangeLog';
import RKPageHeader from '@/components/RKPageHeader';
import BaseContext from '@/Context/BaseContext';
import UserContext from '@/Context/UserContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useUserList } from '@/hooks/useUserList';
import CreateModal from '@/pages/Project/components/CreateModal';
import { createPartner, selectByPartnerId, updatePartner } from '@/services/oa/partner';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { Access, useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { Button, Collapse, message } from 'antd';
import dayjs from 'dayjs';
import { produce } from 'immer';
import { useCallback, useEffect, useRef, useState } from 'react';
import { usePageInfo } from '../../hooks/usePageInfo';
import BaseInfo from './BaseInfo';
import ContractInfo from './ContractInfo';
import Project from './Project';
import Sales from './Sales';
import SalesPlanInformation from './SalesPlanInformation';

const CustomerDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const { approvalDetails } = useModel('useApprovalModel');
  const [visibleCreateProjectModal, setCreateProjectModalVisible] = useState(false);

  const { userList, loading } = useUserList(true);

  const formRef = useRef<ProFormInstance>();
  const codeRef = useRef(0);

  const { pageType } = usePageInfo();

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createPartner(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updatePartner(value), {
    manual: true,
    onSuccess: (res) => {
      codeRef.current = res.code;
      if (res.code !== 200) return;
      message.success('保存成功！');
    },
    formatResult: (res) => res,
  });
  // 判断是否为审批页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');

  // 详情
  const { data } = useRequest(
    () =>
      selectByPartnerId({
        idReq: { id },
      }),
    {
      ready: isEditPage && !isApprovalPage,
      onSuccess: (res) => {
        formRef.current?.setFieldsValue(res);
      },
    },
  );
  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  /**
   * 可编辑条件
   * 未审批
   * 新建页面
   * 非审批中状态，超级权限可以编辑
   */
  const {
    canAddCustomer,
    canEditCustomer,
    canSuperEditCustomer,
    canAddTenderingAgency,
    canEditTenderingAgency,
    canSuperEditTenderingAgency,
    canAddSupplier,
    canEditSupplier,
    canSuperEditSupplier,
    canAddPreSalesProject = false,
  } = useAccess();

  const pageEditMap: Record<string, any> = {
    CLIENT: canEditCustomer,
    INSTITUTION: canEditTenderingAgency,
    VENDOR: canEditSupplier,
  };
  const pageAddMap: Record<string, any> = {
    CLIENT: canAddCustomer,
    INSTITUTION: canAddTenderingAgency,
    VENDOR: canAddSupplier,
  };
  const pageSuperMap: Record<string, any> = {
    CLIENT: canSuperEditCustomer,
    INSTITUTION: canSuperEditTenderingAgency,
    VENDOR: canSuperEditSupplier,
  };

  const canEdit =
    (isEditPage && pageEditMap[pageType] && data?.partnerInfo?.activiStatus !== '1') ||
    (!isEditPage && pageAddMap[pageType]);

  const disabledBase =
    isApprovalPage ||
    !canEdit ||
    (data?.partnerInfo?.activiStatus === '2' && !pageSuperMap[pageType]);

  // 如果是审批页面 则数据从缓存里面取

  useEffect(() => {
    if (isApprovalPage) formRef.current?.setFieldsValue?.(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 只有状态为通过的合同才能新增项目
  const canAddProject = data?.partnerInfo?.activiStatus === '2';

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.PartnerResp>
        formRef={formRef}
        disabled={!canEdit || isApprovalPage}
        initialValues={{
          partnerInfo: {
            partnerType: pageType, //CLIENT-客户 INSTITUTION-机构 VENDOR-供应商
            clientCreateTime: dayjs().format('YYYY-MM-DD'),
          },
        }}
        onValuesChange={(_, values) => {
          // 客户简称-年度-售前项目
          // 如果勾选了创建售前项目

          if (values?.partnerInfo?.hasCrProject === '1') {
            formRef.current?.setFieldsValue({
              partnerInfo: {
                projectName: `${values?.partnerInfo?.clientAbbreviation || ''}-${dayjs().format(
                  'YYYY',
                )}-售前项目`,
              },
            });
          }
        }}
        submitter={
          canEdit && !isApprovalPage
            ? {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },

                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: addLoading || editLoading,
                },
              }
            : false
        }
        onFinish={async (values) => {
          const updateData = produce(values || [], (draft) => {
            return {
              ...draft,
              partnerInfo: {
                ...draft.partnerInfo,
                clientMangers: userList
                  ?.filter((item) =>
                    (
                      draft?.partnerInfo?.clientMangers?.map(
                        (item) => item.clientMangerId || item,
                      ) as string[]
                    )?.includes(item.id!),
                  )
                  ?.map((item) => ({
                    clientMangerId: item.id,
                    clientMangerName: item.employeeName,
                  })),
              },
            };
          });

          if (isEditPage) {
            update(updateData);
          } else {
            add(updateData);
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency
            name={[
              ['partnerInfo', 'activiStatus'],
              ['partnerInfo', 'clientNumber'],
            ]}
          >
            {({ partnerInfo }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : partnerInfo?.activiStatus
                  }
                  title={partnerInfo?.clientNumber}
                  approveType="BUSINESS_PARTNER"
                  onOperationCallback={() => {
                    // 如果提交审核成功，手动改状态，不调用刷新是为了防止页面闪动。
                    if (!isApprovalPage) {
                      formRef.current?.setFieldValue(['partnerInfo', 'activiStatus'], '1');
                    }
                  }}
                  onSave={onSave}
                  saveDisabled={!pageEditMap[pageType]}
                  saveLoading={editLoading}
                />
              );
            }}
          </ProFormDependency>
        )}
        <BaseContext.Provider value={{ disabledBase }}>
          <UserContext.Provider value={{ userList, loading }}>
            <Collapse defaultActiveKey={['1', '2', '3', '4', '5']} ghost>
              <Collapse.Panel key="1" header="基础信息" collapsible="header">
                <BaseInfo />
              </Collapse.Panel>
              <Collapse.Panel key="2" header="追加销售" collapsible="header">
                <Sales />
              </Collapse.Panel>
              {isEditPage && pageType === 'CLIENT' && (
                <>
                  <Collapse.Panel key="3" header="销售计划信息表" collapsible="header">
                    <ProForm.Item
                      name="salePlanList"
                      getValueProps={(val) => ({
                        value: val?.map((item: Record<string, any>) => ({
                          ...item,
                          key_: item.key_ || getRandomId(),
                        })),
                      })}
                    >
                      <SalesPlanInformation />
                    </ProForm.Item>
                  </Collapse.Panel>

                  <Collapse.Panel key="4" header="执行合同信息表" collapsible="header">
                    <ProForm.Item name="executeContractList">
                      <ContractInfo />
                    </ProForm.Item>
                  </Collapse.Panel>

                  <Collapse.Panel
                    key="5"
                    header="项目列表"
                    extra={
                      <Access accessible={canAddPreSalesProject}>
                        <Button
                          key="primary"
                          type="primary"
                          onClick={() => {
                            setCreateProjectModalVisible(true);
                          }}
                          disabled={!canAddProject}
                        >
                          申请售前项目
                        </Button>
                      </Access>
                    }
                    collapsible="header"
                  >
                    {/* 只包含提交审批的所有项目 */}
                    <ProForm.Item name="brieflyProInfoList">
                      <Project />
                    </ProForm.Item>
                  </Collapse.Panel>
                </>
              )}
            </Collapse>
          </UserContext.Provider>
        </BaseContext.Provider>
        <ChangeLog />
      </ProForm>
      {/* 创建售前项目 */}
      <CreateModal
        initialValues={data?.partnerInfo || {}}
        type="SQ"
        visible={visibleCreateProjectModal}
        setVisible={setCreateProjectModalVisible}
      />
    </PageContainer>
  );
};

export default withRouteEditing(CustomerDetails);
